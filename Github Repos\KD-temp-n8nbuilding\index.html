<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice Generator</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        body {
            font-family: Inter, ui-sans-serif, system-ui, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0a0a0a 100%);
            min-height: 100vh;
            padding: 40px 20px;
            color: hsl(0, 0%, 98%);
            overflow-x: hidden;
            -webkit-font-smoothing: antialiased;
            font-feature-settings: "ss01", "ss02", "cv01", "cv02", "cv03";
            line-height: inherit;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, rgba(37, 99, 235, 0.12) 0%, transparent 50%),
                        radial-gradient(circle at 40% 40%, rgba(29, 78, 216, 0.08) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            display: flex;
            flex-direction: column;
            justify-content: center;
            background-color: rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(12px);
            border-radius: 0.75rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 20px 25px -5px rgb(0 0 0 / .1),
                        0 8px 10px -6px rgb(0 0 0 / .1);
            overflow: hidden;
            position: relative;
            padding: 0;
        }

        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(12px);
            color: hsl(0, 0%, 98%);
            padding: 2rem;
            border-radius: 0.75rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 20px 25px -5px rgb(0 0 0 / .1),
                        0 8px 10px -6px rgb(0 0 0 / .1);
            position: relative;
            overflow: hidden;
            margin: 1rem;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: transparent;
            pointer-events: none;
        }

        .header-left {
            display: flex;
            align-items: center;
        }

        .logo {
            width: 120px;
            height: 120px;
            margin-right: 30px;
            background: url('https://www.kdcapital.com/wp-content/uploads/2019/08/logo-white-1.png') no-repeat center;
            background-size: contain;
            filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.3));
        }

        .company-info h1 {
            font-size: 1.8em;
            margin-bottom: 8px;
            font-weight: 700;
            background: linear-gradient(135deg, #ffffff 0%, #e0e0e0 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .company-info p {
            font-size: 0.9em;
            opacity: 0.8;
            line-height: 1.5;
            font-weight: 400;
        }

        .header-right {
            text-align: right;
            position: relative;
        }

        .header-right h2 {
            font-size: 2.4em;
            font-weight: 200;
            letter-spacing: 4px;
            background: linear-gradient(135deg, #ffffff 0%, #cccccc 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-transform: uppercase;
        }
        
        .form-section {
            display: flex;
            flex-direction: column;
            justify-content: center;
            padding: 2rem;
            background-color: rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(12px);
            border-radius: 0.75rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 20px 25px -5px rgb(0 0 0 / .1),
                        0 8px 10px -6px rgb(0 0 0 / .1);
            position: relative;
            margin: 1rem;
        }

        .step {
            margin-bottom: 40px;
            position: relative;
            animation: fade-in 0.6s ease-out 150ms forwards;
            overflow: hidden;
            border-radius: 0.75rem;
            border-width: 1px;
            border-color: rgba(59, 130, 246, 0.2);
            background-image: linear-gradient(to bottom right, #0f172a, #1e293b, #0f172a);
            padding: 1.5rem;
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                        border-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                        box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: rgba(59, 130, 246, 0.004) 0px 0.256704px 0.385056px -0.0770111px,
                        rgba(59, 130, 246, 0.004) 0px 0.102682px 0.154022px -0.102682px;
        }

        .step::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #3b82f6, #2563eb, #1d4ed8);
        }

        .step:hover {
            transform: translateY(0px) scale(1.04);
            box-shadow: rgba(59, 130, 246, 0.09) 0px 15px 25px -15px,
                        rgba(59, 130, 246, 0.06) 0px 8px 12px -8px;
        }

        @keyframes fade-in {
            from {
                opacity: 0;
                transform: translateY(10px) scale(1);
            }
            to {
                opacity: 1;
                transform: translateY(0px) scale(1);
            }
        }

        .step h2 {
            color: hsl(0, 0%, 98%);
            margin-bottom: 24px;
            font-size: 1.5em;
            font-weight: 600;
            position: relative;
            padding-bottom: 12px;
        }

        .step h2::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 80px;
            height: 4px;
            background: linear-gradient(90deg, #3b82f6, #2563eb, #1d4ed8);
            border-radius: 2px;
        }
        
        .form-group {
            margin-bottom: 25px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: hsl(0, 0%, 98%);
            font-size: 0.95em;
        }

        /* Keep loading and error text white */
        .loading,
        .error,
        .success {
            color: hsl(0, 0%, 98%);
        }

        .loading p {
            color: hsl(0, 0%, 98%);
        }

        input, select, textarea {
            width: 100%;
            padding: 16px 20px;
            border: 2px solid rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            font-size: 16px;
            font-weight: 400;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            font-family: 'Inter', sans-serif;
        }

        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.15),
                        0 8px 25px rgba(59, 130, 246, 0.2);
            background: rgba(255, 255, 255, 0.95);
            transform: scale(1.01);
        }

        input:hover, select:hover, textarea:hover {
            border-color: rgba(59, 130, 246, 0.4);
        }
        
        .btn {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 50%, #1d4ed8 100%);
            color: white;
            padding: 18px 36px;
            border: none;
            border-radius: 16px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            position: relative;
            overflow: hidden;
            font-family: 'Inter', sans-serif;
            text-transform: uppercase;
            letter-spacing: 0.8px;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15),
                        0 0 40px rgba(59, 130, 246, 0.6);
        }

        .btn:active {
            transform: translateY(-1px);
        }

        .btn:disabled {
            background: #95a5a6;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .spinner {
            border: 4px solid #ecf0f1;
            border-top: 4px solid #222426;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error {
            background: #fdf2f2;
            border: 1px solid #e74c3c;
            color: #c0392b;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
            font-weight: 500;
        }

        .success {
            background: #f0f9f0;
            border: 1px solid #27ae60;
            color: #1e8449;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
            font-weight: 500;
        }
        
        .hidden {
            display: none;
        }
        
        .form-row {
            display: flex;
            gap: 20px;
        }
        
        .form-row .form-group {
            flex: 1;
        }
        
        @media (max-width: 600px) {
            .form-row {
                flex-direction: column;
                gap: 0;
            }

            .header {
                flex-direction: column;
                text-align: center;
            }

            .header-left {
                margin-bottom: 20px;
            }

            .logo {
                margin: 0 auto 15px;
            }
        }

        /* Invoice-style table for better organization */
        .invoice-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 25px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #dee2e6;
        }

        .detail-section h3 {
            color: #222426;
            font-size: 1.1em;
            margin-bottom: 10px;
            font-weight: 600;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 5px;
        }

        .terms-checkbox {
            display: flex;
            align-items: flex-start;
            margin-bottom: 15px;
            padding: 18px;
            border: 1px solid #d5dbdb;
            border-radius: 6px;
            background: white;
            transition: all 0.3s ease;
        }

        .terms-checkbox:hover {
            border-color: #222426;
            box-shadow: 0 2px 8px rgba(34, 36, 38, 0.1);
        }

        .terms-checkbox input[type="checkbox"] {
            width: auto;
            margin-right: 12px;
            margin-top: 3px;
            transform: scale(1.2);
        }

        .terms-checkbox-content {
            flex: 1;
        }

        .terms-checkbox-title {
            font-weight: 600;
            color: #222426;
            margin-bottom: 8px;
            font-size: 1.05em;
        }

        .terms-checkbox-text {
            font-size: 14px;
            color: #5d6d7e;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-left">
                <div class="logo"></div>
                <div class="company-info">
                    <h1>KD CAPITAL EQUIPMENT</h1>
                    <p>7918 E McClain Drive – Suite 101 – Scottsdale, AZ 85260<br>
                    Tel: (************* | Fax: (*************</p>
                </div>
            </div>
            <div class="header-right">
                <h2>INVOICE<br>DRAFTER</h2>
            </div>
        </div>
        
        <div class="form-section">
            <!-- Step 1: Account ID Input -->
            <div class="step" id="step1">
                <h2>Step 1: Load Account Data</h2>
                <div class="form-group">
                    <label for="accountId">Account ID (from Salesforce):</label>
                    <input type="text" id="accountId" placeholder="e.g., A-1774170" />
                </div>
                <button class="btn" onclick="loadAccountData()">Load Account Data</button>
                
                <div class="loading" id="loading">
                    <div class="spinner"></div>
                    <p>Loading Salesforce data...</p>
                </div>
                
                <div id="error-message"></div>
            </div>
            
            <!-- Step 2: Invoice Form (Hidden initially) -->
            <div class="step hidden" id="step2">
                <h2>Step 2: Invoice Details</h2>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="companyName">Company Name:</label>
                        <input type="text" id="companyName" readonly />
                    </div>
                    <div class="form-group">
                        <label for="salesman">Salesman:</label>
                        <input type="text" id="salesman" value="Chris Loy" />
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="billingAddress">Billing Address:</label>
                    <textarea id="billingAddress" rows="3" readonly></textarea>
                </div>
                
                <div class="form-group">
                    <label for="shipToAddress">Ship To Address:</label>
                    <textarea id="shipToAddress" rows="3"></textarea>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="contactSelect">Contact:</label>
                        <select id="contactSelect">
                            <option value="">Select Contact...</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="refSelect">Reference Number:</label>
                        <select id="refSelect">
                            <option value="">Select Reference...</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="price">Price ($):</label>
                        <input type="number" id="price" step="0.01" placeholder="Enter price manually" />
                    </div>
                    <div class="form-group">
                        <label for="invoiceDate">Invoice Date:</label>
                        <input type="date" id="invoiceDate" />
                    </div>
                </div>

                <div class="form-group">
                    <label>Terms (select applicable):</label>
                    <div id="termsCheckboxes" style="margin-bottom: 15px;">
                        <!-- Checkboxes will be populated here -->
                    </div>
                    <label for="termsText">Terms Text (editable):</label>
                    <textarea id="termsText" rows="6" placeholder="Selected terms will appear here and can be edited..."></textarea>
                </div>
                
                <div class="form-group">
                    <label for="notes">Notes/Description:</label>
                    <textarea id="notes" rows="4" placeholder="Additional notes or item description..."></textarea>
                </div>
                
                <div style="text-align: center; margin-top: 30px;">
                    <button class="btn" onclick="generateInvoice()">Generate Invoice</button>
                </div>
                
                <div id="invoice-result"></div>
            </div>
        </div>
    </div>

    <!-- Color/Opacity Testing Grid -->
    <div style="max-width: 1200px; margin: 40px auto; padding: 20px;">
        <h3 style="color: white; text-align: center; margin-bottom: 30px; font-family: Inter;">🎨 Color & Opacity Samples</h3>
        <div style="display: grid; grid-template-columns: repeat(5, 1fr); gap: 20px;">

            <!-- Row 1: Black variations -->
            <div style="background: rgba(0, 0, 0, 0.2); backdrop-filter: blur(12px); border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 12px; padding: 20px; color: white; text-align: center;">
                <strong>Black 0.2</strong><br>rgba(0,0,0,0.2)
            </div>
            <div style="background: rgba(0, 0, 0, 0.3); backdrop-filter: blur(12px); border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 12px; padding: 20px; color: white; text-align: center;">
                <strong>Black 0.3</strong><br>rgba(0,0,0,0.3)
            </div>
            <div style="background: rgba(0, 0, 0, 0.4); backdrop-filter: blur(12px); border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 12px; padding: 20px; color: white; text-align: center;">
                <strong>Black 0.4</strong><br>rgba(0,0,0,0.4)
            </div>
            <div style="background: rgba(0, 0, 0, 0.5); backdrop-filter: blur(12px); border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 12px; padding: 20px; color: white; text-align: center;">
                <strong>Black 0.5</strong><br>rgba(0,0,0,0.5)
            </div>
            <div style="background: rgba(0, 0, 0, 0.6); backdrop-filter: blur(12px); border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 12px; padding: 20px; color: white; text-align: center;">
                <strong>Black 0.6</strong><br>rgba(0,0,0,0.6)
            </div>

            <!-- Row 2: Dark gray variations -->
            <div style="background: rgba(26, 27, 35, 0.3); backdrop-filter: blur(12px); border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 12px; padding: 20px; color: white; text-align: center;">
                <strong>Dark Gray 0.3</strong><br>rgba(26,27,35,0.3)
            </div>
            <div style="background: rgba(26, 27, 35, 0.4); backdrop-filter: blur(12px); border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 12px; padding: 20px; color: white; text-align: center;">
                <strong>Dark Gray 0.4</strong><br>rgba(26,27,35,0.4)
            </div>
            <div style="background: rgba(26, 27, 35, 0.5); backdrop-filter: blur(12px); border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 12px; padding: 20px; color: white; text-align: center;">
                <strong>Dark Gray 0.5</strong><br>rgba(26,27,35,0.5)
            </div>
            <div style="background: rgba(26, 27, 35, 0.6); backdrop-filter: blur(12px); border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 12px; padding: 20px; color: white; text-align: center;">
                <strong>Dark Gray 0.6</strong><br>rgba(26,27,35,0.6)
            </div>
            <div style="background: rgba(26, 27, 35, 0.7); backdrop-filter: blur(12px); border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 12px; padding: 20px; color: white; text-align: center;">
                <strong>Dark Gray 0.7</strong><br>rgba(26,27,35,0.7)
            </div>

            <!-- Row 3: Blue-black variations -->
            <div style="background: rgba(15, 23, 42, 0.3); backdrop-filter: blur(12px); border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 12px; padding: 20px; color: white; text-align: center;">
                <strong>Blue-Black 0.3</strong><br>rgba(15,23,42,0.3)
            </div>
            <div style="background: rgba(15, 23, 42, 0.4); backdrop-filter: blur(12px); border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 12px; padding: 20px; color: white; text-align: center;">
                <strong>Blue-Black 0.4</strong><br>rgba(15,23,42,0.4)
            </div>
            <div style="background: rgba(15, 23, 42, 0.5); backdrop-filter: blur(12px); border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 12px; padding: 20px; color: white; text-align: center;">
                <strong>Blue-Black 0.5</strong><br>rgba(15,23,42,0.5)
            </div>
            <div style="background: rgba(15, 23, 42, 0.6); backdrop-filter: blur(12px); border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 12px; padding: 20px; color: white; text-align: center;">
                <strong>Blue-Black 0.6</strong><br>rgba(15,23,42,0.6)
            </div>
            <div style="background: rgba(15, 23, 42, 0.7); backdrop-filter: blur(12px); border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 12px; padding: 20px; color: white; text-align: center;">
                <strong>Blue-Black 0.7</strong><br>rgba(15,23,42,0.7)
            </div>

            <!-- Row 4: Neutral variations -->
            <div style="background: rgba(38, 38, 38, 0.3); backdrop-filter: blur(12px); border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 12px; padding: 20px; color: white; text-align: center;">
                <strong>Neutral 0.3</strong><br>rgba(38,38,38,0.3)
            </div>
            <div style="background: rgba(38, 38, 38, 0.4); backdrop-filter: blur(12px); border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 12px; padding: 20px; color: white; text-align: center;">
                <strong>Neutral 0.4</strong><br>rgba(38,38,38,0.4)
            </div>
            <div style="background: rgba(38, 38, 38, 0.5); backdrop-filter: blur(12px); border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 12px; padding: 20px; color: white; text-align: center;">
                <strong>Neutral 0.5</strong><br>rgba(38,38,38,0.5)
            </div>
            <div style="background: rgba(38, 38, 38, 0.6); backdrop-filter: blur(12px); border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 12px; padding: 20px; color: white; text-align: center;">
                <strong>Neutral 0.6</strong><br>rgba(38,38,38,0.6)
            </div>
            <div style="background: rgba(38, 38, 38, 0.7); backdrop-filter: blur(12px); border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 12px; padding: 20px; color: white; text-align: center;">
                <strong>Neutral 0.7</strong><br>rgba(38,38,38,0.7)
            </div>

        </div>

        <p style="color: white; text-align: center; margin-top: 20px; font-family: Inter; opacity: 0.8;">
            👆 Pick your favorite colors and tell me the values to update the form!
        </p>
    </div>

    <script>
        // Configuration - Your n8n webhook URL
        const N8N_WEBHOOK_URL = 'https://oakhill007.app.n8n.cloud/webhook/invoice-form';

        let accountData = null;

        // Set today's date as default
        document.getElementById('invoiceDate').value = new Date().toISOString().split('T')[0];

        async function loadAccountData() {
            const accountId = document.getElementById('accountId').value.trim();

            if (!accountId) {
                showError('Please enter an Account ID');
                return;
            }

            // Show loading
            document.getElementById('loading').style.display = 'block';
            document.getElementById('error-message').innerHTML = '';

            try {
                const response = await fetch(N8N_WEBHOOK_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ accountId: accountId })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                accountData = data;

                // Populate form with loaded data
                populateForm(data);

                // Show step 2
                document.getElementById('step2').classList.remove('hidden');

                // Hide loading
                document.getElementById('loading').style.display = 'none';

                showSuccess('Account data loaded successfully!');

            } catch (error) {
                console.error('Error loading account data:', error);
                showError('Failed to load account data. Please check the Account ID and try again.');
                document.getElementById('loading').style.display = 'none';
            }
        }

        function populateForm(data) {
            const defaults = data.defaults;

            // Populate basic fields
            document.getElementById('companyName').value = defaults.coName || '';
            document.getElementById('billingAddress').value = defaults.billingAddress || '';
            document.getElementById('shipToAddress').value = defaults.shipToAddress || '';

            // Populate terms checkboxes
            const termsCheckboxes = document.getElementById('termsCheckboxes');
            termsCheckboxes.innerHTML = '';

            if (defaults.termsOptions) {
                defaults.termsOptions.forEach((term, index) => {
                    const checkboxDiv = document.createElement('div');
                    checkboxDiv.className = 'terms-checkbox';

                    checkboxDiv.innerHTML = `
                        <input type="checkbox" id="term_${index}" value="${term.field}" onchange="updateTermsText()">
                        <div class="terms-checkbox-content">
                            <div class="terms-checkbox-title">${term.label}</div>
                            <div class="terms-checkbox-text">${term.content}</div>
                        </div>
                    `;

                    termsCheckboxes.appendChild(checkboxDiv);
                });
            }

            // Populate contact dropdown
            const contactSelect = document.getElementById('contactSelect');
            contactSelect.innerHTML = '<option value="">Select Contact...</option>';

            if (defaults.contactOptions) {
                defaults.contactOptions.forEach(contact => {
                    const option = document.createElement('option');
                    option.value = contact.value;
                    option.textContent = contact.label;
                    option.dataset.email = contact.email || '';
                    option.dataset.phone = contact.phone || '';
                    contactSelect.appendChild(option);
                });

                // Set default contact if available
                if (defaults.defaultContact) {
                    contactSelect.value = defaults.defaultContact.value;
                }
            }

            // Populate reference dropdown
            const refSelect = document.getElementById('refSelect');
            refSelect.innerHTML = '<option value="">Select Reference...</option>';

            if (defaults.refOptions) {
                defaults.refOptions.forEach(ref => {
                    const option = document.createElement('option');
                    option.value = ref.value;
                    option.textContent = ref.label;
                    option.dataset.price = ref.price || '';
                    option.dataset.refNumber = ref.refNumber || '';
                    option.dataset.machineTitle = ref.machineTitle || '';
                    refSelect.appendChild(option);
                });

                // Set default reference if available
                if (defaults.defaultRef) {
                    refSelect.value = defaults.defaultRef.value;
                    // Note: Price is NOT auto-filled - sales rep enters manually
                }
            }

            // Note: Removed price auto-population - sales rep enters price manually
        }

        async function generateInvoice() {
            // Collect form data
            const invoiceData = {
                accountId: document.getElementById('accountId').value,
                companyName: document.getElementById('companyName').value,
                salesman: document.getElementById('salesman').value,
                billingAddress: document.getElementById('billingAddress').value,
                shipToAddress: document.getElementById('shipToAddress').value,
                contact: getSelectedContactData(),
                reference: getSelectedReferenceData(),
                terms: getSelectedTermsData(),
                price: document.getElementById('price').value,
                invoiceDate: document.getElementById('invoiceDate').value,
                notes: document.getElementById('notes').value,
                originalData: accountData
            };

            // For now, just show the data (we'll build the submission webhook later)
            console.log('Invoice Data:', invoiceData);
            showSuccess('Invoice data prepared! (Check browser console for details)');

            // TODO: Send to n8n webhook for PDF generation and email
        }

        function getSelectedContactData() {
            const select = document.getElementById('contactSelect');
            const option = select.options[select.selectedIndex];
            return {
                id: select.value,
                name: option.textContent,
                email: option.dataset.email,
                phone: option.dataset.phone
            };
        }

        function getSelectedReferenceData() {
            const select = document.getElementById('refSelect');
            const option = select.options[select.selectedIndex];
            return {
                id: select.value,
                label: option.textContent,
                price: option.dataset.price,
                refNumber: option.dataset.refNumber,
                machineTitle: option.dataset.machineTitle
            };
        }

        function updateTermsText() {
            const checkboxes = document.querySelectorAll('#termsCheckboxes input[type="checkbox"]:checked');
            const termsTextArea = document.getElementById('termsText');

            let combinedTerms = [];
            checkboxes.forEach(checkbox => {
                const termDiv = checkbox.closest('.terms-checkbox');
                const termText = termDiv.querySelector('.terms-checkbox-text').textContent;
                combinedTerms.push(termText);
            });

            termsTextArea.value = combinedTerms.join('\n\n');
        }

        function getSelectedTermsData() {
            const checkboxes = document.querySelectorAll('#termsCheckboxes input[type="checkbox"]:checked');
            const selectedTerms = [];

            checkboxes.forEach(checkbox => {
                const termDiv = checkbox.closest('.terms-checkbox');
                const title = termDiv.querySelector('.terms-checkbox-title').textContent;
                const content = termDiv.querySelector('.terms-checkbox-text').textContent;

                selectedTerms.push({
                    field: checkbox.value,
                    label: title,
                    content: content
                });
            });

            return {
                selectedTerms: selectedTerms,
                finalText: document.getElementById('termsText').value
            };
        }

        function showError(message) {
            document.getElementById('error-message').innerHTML =
                `<div class="error">${message}</div>`;
        }

        function showSuccess(message) {
            document.getElementById('error-message').innerHTML =
                `<div class="success">${message}</div>`;
        }
    </script>
</body>
</html>
